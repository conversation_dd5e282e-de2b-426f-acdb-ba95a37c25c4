package domain

import (
	"errors"
	"time"
)

var (
	ErrRecordNotFound = errors.New("no record found")

	ErrInvalidTemplate = errors.New("invalid payload")

	ErrConflictTemplate = errors.New("template already exists to this connection")
)

type Templates []*Template

type Template struct {
	TemplateID   string     `json:"template_id" bson:"template_id"`
	ConnectionID string     `json:"connection_id" bson:"connection_id"`
	IsDefault    bool       `json:"is_default,omitempty" bson:"is_default"`
	Body         string     `json:"body,omitempty" bson:"body"`
	HeaderText   Text       `json:"header_text,omitempty" bson:"header_text"`
	HeaderLogo   Logo       `json:"header_logo,omitempty" bson:"header_logo"`
	FooterText   Text       `json:"footer_text,omitempty" bson:"footer_text"`
	FooterLogo   Logo       `json:"footer_logo,omitempty" bson:"footer_logo"`
	Connection   Connection `json:"connection" bson:"connection"`
	LastUpdated  time.Time  `json:"last_updated,omitempty" bson:"last_updated"`
}

type Text struct {
	Font  string `json:"font,omitempty" bson:"font"`
	Style string `json:"style,omitempty" bson:"style"`
	Color string `json:"color,omitempty" bson:"color"`
	Value string `json:"value,omitempty" bson:"value"`
}

type Logo struct {
	Name   string `json:"name" bson:"name"`
	Src    string `json:"src,omitempty" bson:"src"`
	Width  int    `json:"width,omitempty" bson:"width"`
	Height int    `json:"height,omitempty" bson:"height"`
}

type Connection struct {
	ConnectionName string `json:"connection_name" bson:"connection_name"`
	CPOURL         string `json:"cpo_url" bson:"cpo_url"`
	CountryCode    string `json:"country_code" bson:"country_code"`
	PartyCode      string `json:"party_code" bson:"party_code"`
}
