package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/port"
)

var _ port.TemplateManager = (*TemplateSrv)(nil)

type TemplateSrv struct {
	manager port.TemplateStoreManager
}

func NewTemplateSrv(manager port.TemplateStoreManager) TemplateSrv {
	return TemplateSrv{
		manager: manager,
	}
}

func (t TemplateSrv) ListTemplate(ctx context.Context) (domain.Templates, error) {
	return t.manager.GetAllTemplates(ctx)
}

func (t TemplateSrv) RetrieveTemplate(ctx context.Context, connectionID, templateID string) (*domain.Template, error) {
	return t.manager.GetTemplate(ctx, connectionID, templateID)
}

func (t TemplateSrv) RetrieveTemplateByCountryAndParty(ctx context.Context, countryCode, partyCode string) (*domain.Template, error) {
	return t.manager.GetTemplateByCountryAndParty(ctx, countryCode, partyCode)
}

func (t TemplateSrv) NewTemplate(ctx context.Context, template *domain.Template) error {
	defaults(template)
	if err := validate(template); err != nil {
		return err
	}
	return t.manager.SaveTemplate(ctx, template)
}

func (t TemplateSrv) ModifyTemplate(ctx context.Context, template *domain.Template) error {
	template.LastUpdated = time.Now()
	return t.manager.UpdateTemplate(ctx, template)
}

func (t TemplateSrv) RemoveTemplate(ctx context.Context, connectionID, templateID string) error {
	return t.manager.DeleteTemplate(ctx, connectionID, templateID)
}

func defaults(template *domain.Template) {
	template.LastUpdated = time.Now()

	if template.TemplateID == "" {
		template.TemplateID = uuid.NewString()
	}
	if template.HeaderText.Font == "" {
		template.HeaderText.Font = "arial"
	}
	if template.HeaderText.Style == "" {
		template.HeaderText.Style = "normal"
	}
	if template.HeaderText.Color == "" {
		template.HeaderText.Color = "#ff0000"
	}
	if template.HeaderText.Value == "" {
		template.HeaderText.Value = "it is sample header text"
	}
	if template.FooterText.Font == "" {
		template.FooterText.Font = "arial"
	}
	if template.FooterText.Style == "" {
		template.FooterText.Style = "normal"
	}
	if template.FooterText.Color == "" {
		template.FooterText.Color = "#ff0000"
	}
	if template.FooterText.Value == "" {
		template.FooterText.Value = "it is sample footer text"
	}
	if template.Body == "" {
		template.Body = "<h4>Sample Body</h4>"
	}
}

func validate(template *domain.Template) error {
	if template.ConnectionID == "" {
		return fmt.Errorf("%w: invalid connection ID", domain.ErrInvalidTemplate)
	}
	if template.TemplateID == "" {
		return fmt.Errorf("%w: invalid template ID", domain.ErrInvalidTemplate)
	}
	if template.Connection.ConnectionName == "" {
		return fmt.Errorf("%w: invalid connection name", domain.ErrInvalidTemplate)
	}
	if template.Connection.CPOURL == "" {
		return fmt.Errorf("%w: invalid cpo url", domain.ErrInvalidTemplate)
	}
	if template.Connection.CountryCode == "" {
		return fmt.Errorf("%w: invalid country code", domain.ErrInvalidTemplate)
	}
	if template.Connection.PartyCode == "" {
		return fmt.Errorf("%w: invalid party code", domain.ErrInvalidTemplate)
	}
	if template.HeaderLogo.Name == "" {
		return fmt.Errorf("%w invalid header logo name", domain.ErrInvalidTemplate)
	}
	if template.FooterLogo.Name == "" {
		return fmt.Errorf("%w invalid footer logo name", domain.ErrInvalidTemplate)
	}
	return nil
}
