package resthttp

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/google/uuid"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

func (h *Handler) GetTemplates(w http.ResponseWriter, r *http.Request) {
	templates, err := h.tmplSrv.ListTemplate(r.Context())
	if err != nil {
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, ToOpenAPIListModel(templates), http.StatusOK)
}

func (h *Handler) GetTemplate(w http.ResponseWriter, r *http.Request, connectionID, templateID string) {
	if _, err := uuid.Parse(templateID); err != nil {
		http.Error(w, "invalid template id", http.StatusBadRequest)
		return
	}

	if _, err := uuid.Parse(connectionID); err != nil {
		http.Error(w, "invalid connection id", http.StatusBadRequest)
		return
	}

	template, err := h.tmplSrv.RetrieveTemplate(r.Context(), connectionID, templateID)
	if err != nil {
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, ToOpenAPIModel(template), http.StatusOK)
}

func (h *Handler) GetTemplateByCountryAndParty(w http.ResponseWriter, r *http.Request, countryCode, partyCode string) {
	// Basic validation for country code (should be 2 characters minimum as per OpenAPI spec)
	if len(countryCode) < 2 {
		http.Error(w, "invalid country code", http.StatusBadRequest)
		return
	}

	// Basic validation for party code (should be at least 1 character as per OpenAPI spec)
	if len(partyCode) < 1 {
		http.Error(w, "invalid party code", http.StatusBadRequest)
		return
	}

	template, err := h.tmplSrv.RetrieveTemplateByCountryAndParty(r.Context(), countryCode, partyCode)
	if err != nil {
		if errors.Is(err, domain.ErrRecordNotFound) {
			http.Error(w, "template not found", http.StatusNotFound)
			return
		}
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, ToOpenAPIModel(template), http.StatusOK)
}

func (h *Handler) NewTemplate(w http.ResponseWriter, r *http.Request, connectionID string) {
	if _, err := uuid.Parse(connectionID); err != nil {
		http.Error(w, "invalid connection id", http.StatusBadRequest)
		return
	}

	var template *domain.Template
	if err := json.NewDecoder(r.Body).Decode(&template); err != nil {
		http.Error(w, "bad request", http.StatusBadRequest)
		return
	}

	// connection is not the part of payload
	if template.ConnectionID == "" {
		template.ConnectionID = connectionID
	}

	if err := h.tmplSrv.NewTemplate(r.Context(), template); err != nil {
		if errors.Is(err, domain.ErrInvalidTemplate) {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
		if errors.Is(err, domain.ErrConflictTemplate) {
			http.Error(w, "connection already exists", http.StatusConflict)
			return
		}
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, nil, http.StatusCreated)
}

func (h *Handler) ModifyTemplate(w http.ResponseWriter, r *http.Request, connectionID, templateID string) {
	if _, err := uuid.Parse(templateID); err != nil {
		http.Error(w, "invalid template id", http.StatusBadRequest)
		return
	}

	if _, err := uuid.Parse(connectionID); err != nil {
		http.Error(w, "invalid connection id", http.StatusBadRequest)
		return
	}

	var template *domain.Template
	if err := json.NewDecoder(r.Body).Decode(&template); err != nil {
		http.Error(w, "bad request", http.StatusBadRequest)
		return
	}
	template.TemplateID = templateID
	template.ConnectionID = connectionID

	if err := h.tmplSrv.ModifyTemplate(r.Context(), template); err != nil {
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, nil, http.StatusNoContent)
}

func (h *Handler) DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionID, templateID string) {
	if _, err := uuid.Parse(templateID); err != nil {
		http.Error(w, "invalid template id", http.StatusBadRequest)
		return
	}

	if _, err := uuid.Parse(connectionID); err != nil {
		http.Error(w, "invalid connection id", http.StatusBadRequest)
		return
	}

	err := h.tmplSrv.RemoveTemplate(r.Context(), connectionID, templateID)
	if err != nil {
		http.Error(w, "something went wrong", http.StatusInternalServerError)
		return
	}

	Success(w, nil, http.StatusNoContent)
}
